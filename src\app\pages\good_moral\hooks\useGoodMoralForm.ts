import { useState } from "react";
import { GoodMoralFormData } from "../types";
import { processImageFile } from "../utils/imageProcessor";
import { generateDocument } from "../utils/documentGenerator";

/**
 * Initial form data with empty values
 */
const initialFormData: GoodMoralFormData = {
  // Personal Information
  "first-name": "",
  "last-name": "",
  "mid-initial": "",
  age: "",
  "pos-address": "",

  // Location Information
  prov: "",
  municipal: "",

  // Date Information
  day: "",
  month: "",
  year: "",

  // Official Information
  mayor: "",
  "ctc-no": "",
  "or-no": "",
  "tin-no": "",

  // Image
  image: "", // base64 image data
};

/**
 * Custom hook for managing Good Moral Certificate form state and actions
 * @returns Form state and handlers
 */
export const useGoodMoralForm = () => {
  const [form, setForm] = useState<GoodMoralFormData>(initialFormData);
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Handles text input changes
   * @param e - Input change event
   */
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  /**
   * Handles image file upload and processing
   * @param file - The uploaded image file
   */
  const handleImageUpload = (file: File) => {
    processImageFile(file, (base64) => {
      setForm((prev) => ({ ...prev, image: base64 }));
    });
  };

  /**
   * Generates and downloads the PDF document
   */
  const handleGenerateDocument = async () => {
    setIsLoading(true);
    try {
      await generateDocument(form);
    } catch (err) {
      console.error("Error generating document:", err);
      alert(err instanceof Error ? err.message : "Something went wrong. Try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    isLoading,
    handleTextChange,
    handleImageUpload,
    handleGenerateDocument,
  };
};
