import { useState } from "react";
import { GoodMoralFormData } from "../types";
import { processImageFile } from "../utils/imageProcessor";
import { generateDocument } from "../utils/documentGenerator";

const initialFormData: GoodMoralFormData = {
  // Personal Information
  "last-name": "",
  "mid-name": "",
  "mid-initial": "",
  age: "",
  "pos-address": "",

  // Location Information
  prov: "",
  municipal: "",

  // Date Information
  day: "",
  month: "",
  year: "",

  // Official Information
  mayor: "",
  "ctc-no": "",
  "or-no": "",

  // Image
  image: "", // base64 image data
};

export const useGoodMoralForm = () => {
  const [form, setForm] = useState<GoodMoralFormData>(initialFormData);
  const [isLoading, setIsLoading] = useState(false);

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    processImageFile(file, (base64) => {
      setForm((prev) => ({ ...prev, image: base64 }));
    });
  };

  const handleGenerateDocument = async () => {
    setIsLoading(true);
    try {
      await generateDocument(form);
    } catch (err) {
      console.error("Error generating document:", err);
      alert(err instanceof Error ? err.message : "Something went wrong. Try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    isLoading,
    handleTextChange,
    handleImageUpload,
    handleGenerateDocument,
  };
};
