import { Button } from "@/components/ui/button";
import {
  Download,
  Printer,
  X,
  ZoomIn,
  ZoomOut,
  RotateCcw,
} from "@deemlol/next-icons";
import { PDFControlsProps } from "../types";

/**
 * PDF viewer control buttons
 */
export const PDFControls: React.FC<
  PDFControlsProps & {
    currentPage: number;
    totalPages: number;
    scale: number;
    onPageChange: (page: number) => void;
    onZoomIn: () => void;
    onZoomOut: () => void;
    onResetZoom: () => void;
    onNextPage: () => void;
    onPrevPage: () => void;
  }
> = ({
  onDownload,
  onPrint,
  onClose,
  fileName,
  isLoading,
  currentPage,
  totalPages,
  scale,
  onPageChange,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onNextPage,
  onPrevPage,
}) => {
  const handlePageInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const page = parseInt(e.target.value);
    if (!isNaN(page) && page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  return (
    <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200 shadow-sm">
      {/* Left side - Navigation */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onPrevPage}
          disabled={currentPage <= 1 || isLoading}
        >
          ←
        </Button>

        <div className="flex items-center space-x-1">
          <input
            type="number"
            value={currentPage}
            onChange={handlePageInput}
            min={1}
            max={totalPages}
            className="w-16 px-2 py-1 text-sm border border-gray-300 rounded text-center"
            disabled={isLoading}
          />
          <span className="text-sm text-gray-600">of {totalPages}</span>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={onNextPage}
          disabled={currentPage >= totalPages || isLoading}
        >
          →
        </Button>
      </div>

      {/* Center - Zoom controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onZoomOut}
          disabled={scale <= 0.5 || isLoading}
        >
          <ZoomOut className="w-4 h-4" />
        </Button>

        <span className="text-sm text-gray-600 min-w-[60px] text-center">
          {Math.round(scale * 100)}%
        </span>

        <Button
          variant="outline"
          size="sm"
          onClick={onZoomIn}
          disabled={scale >= 3.0 || isLoading}
        >
          <ZoomIn className="w-4 h-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={onResetZoom}
          disabled={isLoading}
        >
          <RotateCcw className="w-4 h-4" />
        </Button>
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center space-x-2">
        {onDownload && (
          <Button
            variant="outline"
            size="sm"
            onClick={onDownload}
            disabled={isLoading}
          >
            <Download className="w-4 h-4 mr-1" />
            Download
          </Button>
        )}

        {onPrint && (
          <Button
            variant="outline"
            size="sm"
            onClick={onPrint}
            disabled={isLoading}
          >
            <Printer className="w-4 h-4 mr-1" />
            Print
          </Button>
        )}

        {onClose && (
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>
    </div>
  );
};
