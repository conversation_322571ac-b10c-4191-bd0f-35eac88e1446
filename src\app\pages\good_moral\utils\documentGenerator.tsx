"use client";

import { useState } from "react";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// --- Types ---
export type GoodMoralFormData = {
  "first-name": string;
  "mid-initial": string;
  "last-name": string;
  "mid-name": string;
  age: string;
  municipal: string;
  prov: string;
  "pos-address": string;
  day: string;
  month: string;
  year: string;
  mayor: string;
  "ctc-no": string;
  "or-no": string;
  image?: string; // Base64 image data
};

// --- Helpers ---
const capitalizeWords = (text: string): string =>
  text
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");

export const generateFullName = (form: GoodMoralFormData): string =>
  `${capitalizeWords(form["last-name"])} ${capitalizeWords(form["mid-name"])} ${form["mid-initial"].toUpperCase()}`.trim();

export const validateForm = (form: GoodMoralFormData): boolean =>
  !!(form["last-name"] && form.prov && form.municipal);

// --- Main Generator ---
export const generateDocument = async (form: GoodMoralFormData): Promise<void> => {
  if (!validateForm(form)) {
    alert("Please fill in all required fields");
    return;
  }

  const container = document.createElement("div");
  Object.assign(container.style, {
    width: "794px",  // A4 width in pixels at 96 DPI
    height: "1123px", // A4 height in pixels at 96 DPI
    padding: "60px",
    fontFamily: "'Times New Roman', serif",
    background: "#fff",
    position: "fixed",
    top: "-9999px",
    left: "-9999px",
    color: "#000",
    boxSizing: "border-box",
  });

  container.innerHTML = `
    <div style="text-align:center; font-size:28px; font-weight:bold; margin-bottom:40px; text-transform:uppercase;">
      Republic of the Philippines<br/>
      Province of ${capitalizeWords(form.prov)}<br/>
      Municipality of ${capitalizeWords(form.municipal)}<br/>
      <div style="margin-top:20px; font-size:24px;">Certificate of Good Moral Character</div>
    </div>

    <div style="display:flex; align-items:flex-start; margin-bottom:30px;">
      <div style="flex:1;">
        <table style="width:100%; border-collapse:collapse; font-size:14px;">
          <tr>
            <td style="width:30%; font-weight:bold; padding:8px 0;">Full Name:</td>
            <td style="border-bottom:1px solid #000; padding:8px 5px;">
              ${generateFullName(form)}
            </td>
          </tr>
          <tr>
            <td style="font-weight:bold; padding:8px 0;">Age:</td>
            <td style="border-bottom:1px solid #000; padding:8px 5px;">${form.age}</td>
          </tr>
          <tr>
            <td style="font-weight:bold; padding:8px 0;">Address:</td>
            <td style="border-bottom:1px solid #000; padding:8px 5px;">
              ${capitalizeWords(form["pos-address"])}, ${capitalizeWords(form.municipal)}, ${capitalizeWords(form.prov)}
            </td>
          </tr>
          <tr>
            <td style="font-weight:bold; padding:8px 0;">CTC No:</td>
            <td style="border-bottom:1px solid #000; padding:8px 5px;">${form["ctc-no"]}</td>
          </tr>
          <tr>
            <td style="font-weight:bold; padding:8px 0;">OR No:</td>
            <td style="border-bottom:1px solid #000; padding:8px 5px;">${form["or-no"]}</td>
          </tr>
        </table>
      </div>
      ${form.image ? `
        <div style="margin-left:30px; text-align:center;">
          <img src="data:image/jpeg;base64,${form.image}"
               style="width:192px; height:192px; border:2px solid #000; object-fit:cover;" />
          <div style="font-size:12px; margin-top:5px;">2x2 Photo</div>
        </div>
      ` : ''}
    </div>

    <div style="font-size:14px; line-height:1.6; margin:40px 0; text-align:justify;">
      <p style="margin-bottom:20px; text-indent:50px;">
        TO WHOM IT MAY CONCERN:
      </p>
      <p style="margin-bottom:20px; text-indent:50px;">
        This is to certify that <strong>${generateFullName(form)}</strong>,
        ${form.age} years old, a resident of ${capitalizeWords(form["pos-address"])},
        ${capitalizeWords(form.municipal)}, ${capitalizeWords(form.prov)},
        is a person of good moral character and has no derogatory record filed in this office.
      </p>
      <p style="margin-bottom:20px; text-indent:50px;">
        This certification is issued upon the request of the above-named person
        for whatever legal purpose it may serve.
      </p>
    </div>

    <div style="font-size:14px; margin-top:60px;">
      <p>Issued this ${form.day} day of ${capitalizeWords(form.month)}, ${form.year}.</p>
    </div>

    <div style="text-align:right; margin-top:80px; font-size:14px;">
      <div style="margin-bottom:60px;">
        <strong>${form.mayor.toUpperCase()}</strong><br />
        Municipal Mayor
      </div>
    </div>
  `;

  document.body.appendChild(container);

  const canvas = await html2canvas(container, {
    scale: 2,
    useCORS: true,
    allowTaint: true,
    backgroundColor: '#ffffff'
  });
  const imgData = canvas.toDataURL("image/png");

  const pdf = new jsPDF("portrait", "px", "a4");
  const imgWidth = 595; // A4 width in pixels
  const imgHeight = 842; // A4 height in pixels

  pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
  pdf.save(`Good_Moral_Certificate(${generateFullName(form)}).pdf`);

  document.body.removeChild(container);
};

// --- Main Component ---
export default function CertificateGenerator() {
  const [form, setForm] = useState<GoodMoralFormData>({
    "first-name": "Brent",
    "mid-name": "Lemmuell",
    "mid-initial": "L",
    "last-name": "Ortega",
    age: "22",
    municipal: "Tanauan",
    prov: "Leyte",
    "pos-address": "Brgy. Mohon",
    day: "16",
    month: "July",
    year: "2025",
    mayor: "Juan Dela Cruz",
    "ctc-no": "123456789",
    "or-no": "987654321",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="p-6 max-w-xl mx-auto space-y-4">
      <h1 className="text-xl font-bold">Good Moral Certificate Form</h1>

      {Object.keys(form).map((key) => (
        <div key={key}>
          <label className="block mb-1 capitalize">{key.replace(/-/g, " ")}:</label>
          <input
            className="w-full p-2 border rounded"
            name={key}
            value={(form as any)[key]}
            onChange={handleChange}
          />
        </div>
      ))}

      <button
        className="bg-blue-600 text-white px-4 py-2 rounded"
        onClick={() => generateDocument(form)}
      >
        Download Certificate PDF
      </button>
    </div>
  );
}
