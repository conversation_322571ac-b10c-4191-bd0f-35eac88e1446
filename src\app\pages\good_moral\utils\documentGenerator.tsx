"use client";

import { useState } from "react";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// --- Types ---
export type GoodMoralFormData = {
  "first-name": string;
  "mid-initial": string;
  "last-name": string;
  "mid-name": string;
  age: string;
  municipal: string;
  prov: string;
  "pos-address": string;
  day: string;
  month: string;
  year: string;
  mayor: string;
  "ctc-no": string;
  "or-no": string;
  "tin-no"?: string; // Tax Identification Number
  image?: string; // Base64 image data
};

// --- Helpers ---
const capitalizeWords = (text: string): string =>
  text
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");

export const generateFullName = (form: GoodMoralFormData): string =>
  `${capitalizeWords(form["last-name"])} ${capitalizeWords(
    form["mid-name"]
  )} ${form["mid-initial"].toUpperCase()}`.trim();

export const validateForm = (form: GoodMoralFormData): boolean =>
  !!(form["last-name"] && form.prov && form.municipal);

// --- Main Generator ---
export const generateDocument = async (
  form: GoodMoralFormData
): Promise<void> => {
  if (!validateForm(form)) {
    alert("Please fill in all required fields");
    return;
  }

  const container = document.createElement("div");
  Object.assign(container.style, {
    width: "794px", // A4 width in pixels at 96 DPI
    height: "1123px", // A4 height in pixels at 96 DPI
    padding: "60px",
    fontFamily: "'Times New Roman', serif",
    background: "#fff",
    position: "fixed",
    top: "-9999px",
    left: "-9999px",
    color: "#000",
    boxSizing: "border-box",
  });

  container.innerHTML = `
    <!-- Header with Logo and Official Information -->
    <div style="display: flex; align-items: flex-start; margin-bottom: 30px;">
      <div style="width: 120px; height: 120px; border: 2px solid #000; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 30px; flex-shrink: 0;">
        ${
          form.image
            ? `
          <img src="data:image/jpeg;base64,${form.image}" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover;" />
        `
            : `
          <div style="font-size: 12px; text-align: center; color: #666;">OFFICIAL<br/>SEAL</div>
        `
        }
      </div>
      <div style="text-align: center; flex: 1; font-size: 16px; font-weight: bold;">
        Republic of the Philippines<br/>
        Province of ${capitalizeWords(form.prov)}<br/>
        Municipality of ${capitalizeWords(form.municipal)}<br/>
        <div style="margin-top: 15px; font-size: 14px; border-bottom: 1px solid #000; display: inline-block; padding-bottom: 2px;">⚜</div>
      </div>
    </div>

    <!-- Title -->
    <div style="text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 40px; text-transform: uppercase;">
      CERTIFICATE OF GOOD MORAL CHARACTER/CLEARANCE
    </div>

    <!-- Main Content -->
    <div style="font-size: 14px; line-height: 1.8; margin-bottom: 30px;">
      <p style="margin-bottom: 20px;">
        TO WHOM IT MAY CONCERN:
      </p>

      <p style="margin-bottom: 20px; text-indent: 50px; text-align: justify;">
        THIS IS TO CERTIFY that <strong><u>${generateFullName(
          form
        )}</u></strong> Filipino, <strong><u>${form.age}</u></strong> years old,
        with postal address at, <strong><u>${capitalizeWords(
          form["pos-address"]
        )}</u></strong> (Municipal), <strong><u>${capitalizeWords(
    form.prov
  )}</u></strong>, Philippines whose thumbprints
        and signature appear herein, is known to be a person of GOOD MORAL CHARACTER. Hence,
        this CLEARANCE is issued to the above-named person.
      </p>

      <p style="margin-bottom: 30px; text-indent: 50px;">
        Issued this <strong><u>${
          form.day
        }TH day</u></strong> of <strong><u>${capitalizeWords(form.month)} (${
    form.year
  })</u></strong> at Tanauan, Leyte, Philippines
      </p>

      ${
        form.image
          ? `
        <div style="text-align: center; margin: 40px 0;">
          <img src="data:image/jpeg;base64,${form.image}" style="width: 120px; height: 120px; border: 2px solid #000; object-fit: cover;" />
        </div>
      `
          : ""
      }
    </div>

    <!-- Mayor Signature -->
    <div style="text-align: center; margin: 60px 0 40px 0;">
      <div style="margin-bottom: 60px;">
        <strong><u>${form.mayor.toUpperCase()}</u></strong><br/>
        <em>Municipal Mayor</em>
      </div>
    </div>

    <!-- Thumbprint and Signature Section -->
    <div style="display: flex; justify-content: space-between; margin: 40px 0;">
      <div style="display: flex; gap: 20px;">
        <div style="text-align: center;">
          <div style="width: 80px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
          <div style="font-size: 12px; font-style: italic;">Left</div>
        </div>
        <div style="text-align: center;">
          <div style="width: 80px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
          <div style="font-size: 12px; font-style: italic;">Thumbmark</div>
        </div>
        <div style="text-align: center;">
          <div style="width: 80px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
          <div style="font-size: 12px; font-style: italic;">Right</div>
        </div>
      </div>

      <div style="text-align: center;">
        <div style="width: 200px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
        <div style="font-size: 12px; font-style: italic;">Documentary Stamp Paid</div>
      </div>
    </div>

    <!-- Signature Line -->
    <div style="text-align: center; margin: 20px 0;">
      <div style="border-bottom: 1px solid #000; width: 300px; margin: 0 auto 5px auto;"></div>
      <div style="font-size: 12px;">
        <strong><u>${capitalizeWords(form["last-name"])}, ${capitalizeWords(
    form["mid-name"]
  )} ${form["mid-initial"].toUpperCase()}</u></strong><br/>
        <em>Signature</em>
      </div>
    </div>

    <!-- Footer Information -->
    <div style="display: flex; justify-content: space-between; margin-top: 40px; font-size: 12px;">
      <div>
        <div>CTC No.: ________________<strong><u>${
          form["ctc-no"]
        }</u></strong></div>
        <div>Issued on: ________________<strong><u>${capitalizeWords(
          form.month
        )} ${form.day}, ${form.year}</u></strong></div>
        <div>Issued at: ________________<strong><u>${capitalizeWords(
          form.municipal
        )}, ${capitalizeWords(form.prov)}</u></strong></div>
        <div>TIN No.: ________________<strong><u>${
          form["tin-no"] || ""
        }</u></strong></div>
      </div>

      <div>
        <div>O.R. No.: ________________<strong><u>${
          form["or-no"]
        }</u></strong></div>
        <div>Issued on: ________________<strong><u>${capitalizeWords(
          form.month
        )} ${form.day}, ${form.year}</u></strong></div>
        <div>Issued at: ________________<strong><u>${capitalizeWords(
          form.municipal
        )}, ${capitalizeWords(form.prov)}</u></strong></div>
      </div>
    </div>
  `;

  document.body.appendChild(container);

  const canvas = await html2canvas(container, {
    scale: 2,
    useCORS: true,
    allowTaint: true,
    backgroundColor: "#ffffff",
  });
  const imgData = canvas.toDataURL("image/png");

  const pdf = new jsPDF("portrait", "px", "a4");
  const imgWidth = 595; // A4 width in pixels
  const imgHeight = 842; // A4 height in pixels

  pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
  pdf.save(`Good_Moral_Certificate(${generateFullName(form)}).pdf`);

  document.body.removeChild(container);
};

// --- Main Component ---
export default function CertificateGenerator() {
  const [form, setForm] = useState<GoodMoralFormData>({
    "first-name": "Brent",
    "mid-name": "Lemmuell",
    "mid-initial": "L",
    "last-name": "Ortega",
    age: "22",
    municipal: "Tanauan",
    prov: "Leyte",
    "pos-address": "Brgy. Mohon",
    day: "16",
    month: "July",
    year: "2025",
    mayor: "Juan Dela Cruz",
    "ctc-no": "123456789",
    "or-no": "987654321",
    "tin-no": "123-456-789-000",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="p-6 max-w-xl mx-auto space-y-4">
      <h1 className="text-xl font-bold">Good Moral Certificate Form</h1>

      {Object.keys(form).map((key) => (
        <div key={key}>
          <label className="block mb-1 capitalize">
            {key.replace(/-/g, " ")}:
          </label>
          <input
            className="w-full p-2 border rounded"
            name={key}
            value={(form as any)[key]}
            onChange={handleChange}
          />
        </div>
      ))}

      <button
        className="bg-blue-600 text-white px-4 py-2 rounded"
        onClick={() => generateDocument(form)}
      >
        Download Certificate PDF
      </button>
    </div>
  );
}
