"use client";

import html2canvas from "html2canvas";
import jsPD<PERSON> from "jspdf";
import { GoodMoralFormData } from "../types";
import {
  DOCUMENT_DIMENSIONS,
  PDF_SETTINGS,
  DEFAULT_VALUES,
  VALIDATION_RULES,
} from "../constants";

// --- Helper Functions ---

/**
 * Capitalizes the first letter of each word in a string
 * @param text - The text to capitalize
 * @returns Capitalized text
 */
const capitalizeWords = (text: string): string =>
  text
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");

/**
 * Generates a full name from form data
 * @param form - The form data containing name fields
 * @returns Formatted full name
 */
export const generateFullName = (form: GoodMoralFormData): string =>
  `${capitalizeWords(form["last-name"])} ${capitalizeWords(
    form["mid-name"]
  )} ${form["mid-initial"].toUpperCase()}`.trim();

/**
 * Validates required form fields
 * @param form - The form data to validate
 * @returns True if form is valid, false otherwise
 */
export const validateForm = (form: GoodMoralFormData): boolean =>
  VALIDATION_RULES.REQUIRED_FIELDS.every((field) => form[field]);

// --- Document Generation ---

/**
 * Generates a PDF document for Good Moral Certificate
 * @param form - The form data containing all certificate information
 * @throws Error if form validation fails or document generation fails
 */
export const generateDocument = async (
  form: GoodMoralFormData
): Promise<void> => {
  if (!validateForm(form)) {
    alert("Please fill in all required fields");
    return;
  }

  const container = document.createElement("div");

  // Create a completely isolated container with no CSS inheritance
  container.className = "pdf-generation-container";
  Object.assign(container.style, {
    width: `${DOCUMENT_DIMENSIONS.WIDTH}px`,
    height: `${DOCUMENT_DIMENSIONS.HEIGHT}px`,
    padding: `${DOCUMENT_DIMENSIONS.PADDING}px`,
    fontFamily: "'Times New Roman', serif",
    background: "#ffffff",
    position: "fixed",
    top: "-9999px",
    left: "-9999px",
    color: "#000000",
    boxSizing: "border-box",
    isolation: "isolate",
    zIndex: "-1",
    // Override any potential CSS variables
    "--tw-bg-opacity": "1",
    "--tw-text-opacity": "1",
    "--tw-border-opacity": "1",
  });

  // Add a style tag to override any potential CSS conflicts
  const styleOverride = document.createElement("style");
  styleOverride.textContent = `
    .pdf-generation-container * {
      all: unset !important;
      display: revert !important;
      box-sizing: border-box !important;
      font-family: 'Times New Roman', serif !important;
      color: #000000 !important;
      background: transparent !important;
      border: none !important;
      margin: 0 !important;
      padding: 0 !important;
    }
    .pdf-generation-container {
      background: #ffffff !important;
      color: #000000 !important;
    }
  `;
  document.head.appendChild(styleOverride);

  container.innerHTML = `
    <!-- Header with Logo and Official Information -->
    <div style="display: flex; align-items: flex-start; margin-bottom: 30px;">
      <div style="width: 120px; height: 120px; border: 2px solid #000; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 30px; flex-shrink: 0;">
        ${
          form.image
            ? `
          <img src="data:image/jpeg;base64,${form.image}" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover;" />
        `
            : `
          <div style="font-size: 12px; text-align: center; color: #666;">OFFICIAL<br/>SEAL</div>
        `
        }
      </div>
      <div style="text-align: center; flex: 1; font-size: 16px; font-weight: bold;">
        Republic of the Philippines<br/>
        Province of ${capitalizeWords(form.prov)}<br/>
        Municipality of ${capitalizeWords(form.municipal)}<br/>
        <div style="margin-top: 15px; font-size: 14px; border-bottom: 1px solid #000; display: inline-block; padding-bottom: 2px;">⚜</div>
      </div>
    </div>

    <!-- Title -->
    <div style="text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 40px; text-transform: uppercase;">
      CERTIFICATE OF GOOD MORAL CHARACTER/CLEARANCE
    </div>

    <!-- Main Content -->
    <div style="font-size: 14px; line-height: 1.8; margin-bottom: 30px;">
      <p style="margin-bottom: 20px;">
        TO WHOM IT MAY CONCERN:
      </p>

      <p style="margin-bottom: 20px; text-indent: 50px; text-align: justify;">
        THIS IS TO CERTIFY that <strong><u>${generateFullName(
          form
        )}</u></strong> ${DEFAULT_VALUES.NATIONALITY}, <strong><u>${
    form.age
  }</u></strong> years old,
        with postal address at, <strong><u>${capitalizeWords(
          form["pos-address"]
        )}</u></strong> (Municipal), <strong><u>${capitalizeWords(
    form.prov
  )}</u></strong>, Philippines whose thumbprints
        and signature appear herein, is known to be a person of GOOD MORAL CHARACTER. Hence,
        this CLEARANCE is issued to the above-named person.
      </p>

      <p style="margin-bottom: 30px; text-indent: 50px;">
        Issued this <strong><u>${
          form.day
        }TH day</u></strong> of <strong><u>${capitalizeWords(form.month)} (${
    form.year
  })</u></strong> at ${DEFAULT_VALUES.LOCATION}
      </p>

      ${
        form.image
          ? `
        <div style="text-align: center; margin: 40px 0;">
          <img src="data:image/jpeg;base64,${form.image}" style="width: 120px; height: 120px; border: 2px solid #000; object-fit: cover;" />
        </div>
      `
          : ""
      }
    </div>

    <!-- Mayor Signature -->
    <div style="text-align: center; margin: 60px 0 40px 0;">
      <div style="margin-bottom: 60px;">
        <strong><u>${form.mayor.toUpperCase()}</u></strong><br/>
        <em>Municipal Mayor</em>
      </div>
    </div>

    <!-- Thumbprint and Signature Section -->
    <div style="display: flex; justify-content: space-between; margin: 40px 0;">
      <div style="display: flex; gap: 20px;">
        <div style="text-align: center;">
          <div style="width: 80px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
          <div style="font-size: 12px; font-style: italic;">Left</div>
        </div>
        <div style="text-align: center;">
          <div style="width: 80px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
          <div style="font-size: 12px; font-style: italic;">Thumbmark</div>
        </div>
        <div style="text-align: center;">
          <div style="width: 80px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
          <div style="font-size: 12px; font-style: italic;">Right</div>
        </div>
      </div>

      <div style="text-align: center;">
        <div style="width: 200px; height: 80px; border: 2px solid #000; margin-bottom: 10px;"></div>
        <div style="font-size: 12px; font-style: italic;">Documentary Stamp Paid</div>
      </div>
    </div>

    <!-- Signature Line -->
    <div style="text-align: center; margin: 20px 0;">
      <div style="border-bottom: 1px solid #000; width: 300px; margin: 0 auto 5px auto;"></div>
      <div style="font-size: 12px;">
        <strong><u>${capitalizeWords(form["last-name"])}, ${capitalizeWords(
    form["mid-name"]
  )} ${form["mid-initial"].toUpperCase()}</u></strong><br/>
        <em>Signature</em>
      </div>
    </div>

    <!-- Footer Information -->
    <div style="display: flex; justify-content: space-between; margin-top: 40px; font-size: 12px;">
      <div>
        <div>CTC No.: ________________<strong><u>${
          form["ctc-no"]
        }</u></strong></div>
        <div>Issued on: ________________<strong><u>${capitalizeWords(
          form.month
        )} ${form.day}, ${form.year}</u></strong></div>
        <div>Issued at: ________________<strong><u>${capitalizeWords(
          form.municipal
        )}, ${capitalizeWords(form.prov)}</u></strong></div>
        <div>TIN No.: ________________<strong><u>${
          form["tin-no"] || ""
        }</u></strong></div>
      </div>

      <div>
        <div>O.R. No.: ________________<strong><u>${
          form["or-no"]
        }</u></strong></div>
        <div>Issued on: ________________<strong><u>${capitalizeWords(
          form.month
        )} ${form.day}, ${form.year}</u></strong></div>
        <div>Issued at: ________________<strong><u>${capitalizeWords(
          form.municipal
        )}, ${capitalizeWords(form.prov)}</u></strong></div>
      </div>
    </div>
  `;

  document.body.appendChild(container);

  try {
    const canvas = await html2canvas(container, {
      scale: PDF_SETTINGS.SCALE,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      logging: false, // Disable logging to reduce console noise
      removeContainer: false, // We'll handle container removal manually
    });

    const imgData = canvas.toDataURL("image/png");

    const pdf = new jsPDF(PDF_SETTINGS.ORIENTATION, "px", PDF_SETTINGS.FORMAT);
    const imgWidth = PDF_SETTINGS.IMAGE_WIDTH;
    const imgHeight = PDF_SETTINGS.IMAGE_HEIGHT;

    pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
    pdf.save(`Good_Moral_Certificate(${generateFullName(form)}).pdf`);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error("Failed to generate PDF. Please try again.");
  } finally {
    // Clean up
    document.body.removeChild(container);
    document.head.removeChild(styleOverride);
  }
};
