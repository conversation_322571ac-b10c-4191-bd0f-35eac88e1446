import { useEffect, useRef, useState } from "react";

interface PDFCanvasProps {
  pdfDocument: any; // PDF.js document type
  pageNumber: number;
  scale: number;
  onLoadError?: (error: string) => void;
}

/**
 * Canvas component for rendering PDF pages
 */
export const PDFCanvas: React.FC<PDFCanvasProps> = ({
  pdfDocument,
  pageNumber,
  scale,
  onLoadError,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isRendering, setIsRendering] = useState(false);

  useEffect(() => {
    if (!pdfDocument || !canvasRef.current) return;

    const renderPage = async () => {
      setIsRendering(true);
      
      try {
        const page = await pdfDocument.getPage(pageNumber);
        const canvas = canvasRef.current!;
        const context = canvas.getContext("2d")!;

        // Calculate viewport
        const viewport = page.getViewport({ scale });
        
        // Set canvas dimensions
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        // Clear canvas
        context.clearRect(0, 0, canvas.width, canvas.height);

        // Render page
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;
      } catch (error) {
        console.error("Error rendering PDF page:", error);
        onLoadError?.(error instanceof Error ? error.message : "Failed to render page");
      } finally {
        setIsRendering(false);
      }
    };

    renderPage();
  }, [pdfDocument, pageNumber, scale, onLoadError]);

  return (
    <div className="flex justify-center items-center min-h-[400px] bg-gray-100">
      {isRendering && (
        <div className="absolute z-10 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}
      <canvas
        ref={canvasRef}
        className="shadow-lg border border-gray-300 bg-white"
        style={{
          maxWidth: "100%",
          height: "auto",
        }}
      />
    </div>
  );
};
