# PDF Viewer Module

A comprehensive PDF viewing solution for certificate generation and preview, designed to be compatible with future certificate types.

## Features

- **Universal PDF Viewer**: Works with any PDF document or blob
- **Certificate Type Support**: Extensible system for different certificate types
- **Interactive Controls**: Navigation, zoom, print, and download functionality
- **Preview System**: Generate and preview documents before final download
- **Responsive Design**: Works on desktop and mobile devices
- **Future-Proof**: Easy to add new certificate types

## Structure

```
pdf-viewer/
├── components/           # React components
│   ├── PDFViewer.tsx    # Main PDF viewer component
│   ├── PDFPreview.tsx   # Preview generator component
│   ├── PDFControls.tsx  # Viewer control buttons
│   ├── PDFCanvas.tsx    # PDF rendering canvas
│   └── index.ts         # Component exports
├── hooks/               # Custom React hooks
│   └── usePDFViewer.ts  # PDF viewer state management
├── utils/               # Utility functions
│   ├── pdfGenerator.ts  # Certificate generator registry
│   └── index.ts         # Utility exports
├── constants.ts         # Configuration constants
├── types.ts            # TypeScript definitions
├── index.ts            # Module exports
├── page.tsx            # Demo page
└── README.md           # This file
```

## Usage

### Basic PDF Viewer

```tsx
import { PDFViewer } from "@/app/pages/pdf-viewer";

function MyComponent() {
  const pdfBlob = new Blob([pdfData], { type: "application/pdf" });
  
  return (
    <PDFViewer
      pdfBlob={pdfBlob}
      certificateType="good_moral"
      fileName="My_Certificate"
      onClose={() => console.log("Viewer closed")}
      onDownload={() => console.log("PDF downloaded")}
      onPrint={() => console.log("PDF printed")}
    />
  );
}
```

### PDF Preview with Generation

```tsx
import { PDFPreview } from "@/app/pages/pdf-viewer";

function CertificateForm() {
  const formData = {
    "first-name": "John",
    "last-name": "Doe",
    // ... other form fields
  };

  return (
    <PDFPreview
      certificateType="good_moral"
      formData={formData}
      onGenerate={(blob) => console.log("PDF generated:", blob)}
      showPreview={true}
    />
  );
}
```

### Using the Hook

```tsx
import { usePDFViewer } from "@/app/pages/pdf-viewer";

function CustomViewer({ pdfUrl }: { pdfUrl: string }) {
  const {
    isLoading,
    error,
    currentPage,
    totalPages,
    scale,
    nextPage,
    prevPage,
    zoomIn,
    zoomOut,
  } = usePDFViewer(pdfUrl);

  // Custom viewer implementation
}
```

## Adding New Certificate Types

### 1. Update Types

```typescript
// types.ts
export type CertificateType = 
  | "good_moral" 
  | "affidavit" 
  | "your_new_type"; // Add your new type
```

### 2. Add Configuration

```typescript
// constants.ts
export const CERTIFICATE_CONFIGS = {
  // ... existing configs
  your_new_type: {
    name: "Your New Certificate",
    description: "Description of your certificate",
    defaultFileName: "Your_New_Certificate",
    previewEnabled: true,
  },
};
```

### 3. Add Generator

```typescript
// utils/pdfGenerator.ts
const PDF_GENERATORS: Record<CertificateType, GeneratorFunction> = {
  // ... existing generators
  your_new_type: async (formData) => {
    const { generateYourCertificate } = await import("../your_certificate/utils");
    return await generateYourCertificate(formData);
  },
};
```

## Components

### PDFViewer
Main component for viewing PDF documents with full controls.

**Props:**
- `pdfUrl?: string` - URL to PDF file
- `pdfBlob?: Blob` - PDF data as blob
- `certificateType: CertificateType` - Type of certificate
- `fileName?: string` - Custom filename for downloads
- `onClose?: () => void` - Close callback
- `onDownload?: () => void` - Download callback
- `onPrint?: () => void` - Print callback

### PDFPreview
Component for generating and previewing certificates.

**Props:**
- `certificateType: CertificateType` - Type of certificate
- `formData: Record<string, any>` - Form data for generation
- `onGenerate?: (blob: Blob) => void` - Generation callback
- `showPreview?: boolean` - Auto-show preview

### PDFControls
Control bar with navigation, zoom, and action buttons.

### PDFCanvas
Canvas component for rendering PDF pages using PDF.js.

## Dependencies

The module uses the following dependencies:

- **PDF.js**: For PDF rendering and manipulation
- **React**: For component framework
- **Tailwind CSS**: For styling
- **Lucide React**: For icons

## Installation

Make sure to install PDF.js:

```bash
pnpm add pdfjs-dist
pnpm add -D @types/pdfjs-dist
```

## Configuration

### PDF Viewer Settings

```typescript
export const PDF_VIEWER_SETTINGS = {
  DEFAULT_SCALE: 1.0,
  MIN_SCALE: 0.5,
  MAX_SCALE: 3.0,
  SCALE_STEP: 0.25,
  CANVAS_MAX_WIDTH: 1200,
  CANVAS_MAX_HEIGHT: 1600,
};
```

### File Configurations

```typescript
export const FILE_CONFIGS = {
  PDF: {
    mimeType: "application/pdf",
    extension: ".pdf",
    maxSize: 10 * 1024 * 1024, // 10MB
  },
};
```

## Best Practices

1. **Always provide error handling** for PDF loading failures
2. **Use appropriate file names** for downloads
3. **Implement loading states** for better UX
4. **Test with different PDF sizes** and formats
5. **Consider mobile responsiveness** for touch interactions
6. **Optimize for performance** with large PDF files

## Future Enhancements

- [ ] Thumbnail navigation
- [ ] Text search within PDFs
- [ ] Annotation support
- [ ] Multiple PDF comparison
- [ ] Batch operations
- [ ] Cloud storage integration
