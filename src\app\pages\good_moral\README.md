# Good Moral Certificate Module

This module provides functionality for generating Good Moral Certificate/Clearance documents in PDF format.

## Structure

```
good_moral/
├── components/           # React components
│   ├── FormSections.tsx  # Form input sections
│   ├── ImageUpload.tsx   # Image upload component
│   └── index.ts         # Component exports
├── hooks/               # Custom React hooks
│   └── useGoodMoralForm.ts # Form state management
├── utils/               # Utility functions
│   ├── documentGenerator.tsx # PDF generation logic
│   ├── imageProcessor.ts     # Image processing utilities
│   └── index.ts             # Utility exports
├── constants.ts         # Application constants
├── types.ts            # TypeScript type definitions
├── page.tsx            # Main page component
└── README.md           # This file
```

## Key Features

- **Form Management**: Comprehensive form with validation for all certificate fields
- **Image Processing**: Upload and process 2x2 photos with automatic cropping and resizing
- **PDF Generation**: Generate official-looking certificates matching government templates
- **Type Safety**: Full TypeScript support with proper type definitions
- **Responsive Design**: Mobile-friendly form interface

## Components

### FormSections
- `LocationSection`: Province and municipality inputs
- `PersonalSection`: Personal information (name, age, address)
- `DateSection`: Certificate issuance date
- `OfficialSection`: Official information (mayor, CTC, O.R., TIN numbers)

### ImageUpload
- Handles file selection and preview
- Processes images to 192x192px format
- Converts to base64 for PDF embedding

## Usage

```tsx
import { useGoodMoralForm } from './hooks/useGoodMoralForm';
import { LocationSection, PersonalSection } from './components';

function MyComponent() {
  const { form, handleTextChange, handleGenerateDocument } = useGoodMoralForm();
  
  return (
    <div>
      <PersonalSection form={form} handleTextChange={handleTextChange} />
      <button onClick={handleGenerateDocument}>Generate PDF</button>
    </div>
  );
}
```

## Configuration

All configuration values are centralized in `constants.ts`:

- Document dimensions (A4 format)
- PDF export settings
- Image processing parameters
- Validation rules
- Default values

## Development

### Adding New Fields
1. Update `GoodMoralFormData` interface in `types.ts`
2. Add field to appropriate form section in `components/FormSections.tsx`
3. Update document template in `utils/documentGenerator.tsx`
4. Add validation rules to `constants.ts` if needed

### Modifying PDF Layout
1. Edit the HTML template in `documentGenerator.tsx`
2. Adjust styling and positioning as needed
3. Test with various data inputs

### Image Processing
- Images are automatically cropped to square format
- Resized to 192x192px for consistent display
- Converted to JPEG format with 80% quality
