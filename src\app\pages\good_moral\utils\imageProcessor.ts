/**
 * Processes an uploaded image file and converts it to base64
 * @param file - The uploaded image file
 * @param callback - Callback function to handle the processed base64 string
 */
export const processImageFile = (
  file: File,
  callback: (base64: string) => void
): void => {
  const reader = new FileReader();
  
  reader.onload = () => {
    const img = new Image();
    img.src = reader.result as string;

    img.onload = () => {
      const canvas = document.createElement("canvas");
      const size = Math.min(img.width, img.height);
      const sx = (img.width - size) / 2;
      const sy = (img.height - size) / 2;

      canvas.width = 192;
      canvas.height = 192;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(img, sx, sy, size, size, 0, 0, 192, 192);
        const base64 = canvas.toDataURL("image/jpeg").split(",")[1];
        callback(base64);
      }
    };
  };
  
  reader.readAsDataURL(file);
};
