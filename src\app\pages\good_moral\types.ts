/**
 * Form data structure for Good Moral Certificate application
 */
export interface GoodMoralFormData {
  // Personal Information
  "first-name": string;
  "last-name": string;
  "mid-initial": string;
  age: string;
  "pos-address": string;

  // Location Information
  prov: string;
  municipal: string;

  // Date Information
  day: string;
  month: string;
  year: string;

  // Official Information
  mayor: string;
  "ctc-no": string;
  "or-no": string;
  "tin-no"?: string;

  // Image
  image?: string; // base64 image data
}

/**
 * Props for form section components
 */
export interface FormSectionProps {
  form: GoodMoralFormData;
  handleTextChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

/**
 * Props for image upload component
 */
export interface ImageUploadProps {
  image?: string;
  onImageUpload: (file: File) => void;
}
