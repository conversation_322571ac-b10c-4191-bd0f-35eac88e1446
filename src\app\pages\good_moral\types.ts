export interface GoodMoralFormData {
  // Personal Information
  "last-name": string;
  "mid-name": string;
  "mid-initial": string;
  age: string;
  "pos-address": string;

  // Location Information
  prov: string;
  municipal: string;

  // Date Information
  day: string;
  month: string;
  year: string;

  // Official Information
  mayor: string;
  "ctc-no": string;
  "or-no": string;

  // Image
  image: string; // base64 image data
}

export interface TemplateData extends Record<string, string> {
  prov: string;
  municipal: string;
  "last-name": string;
  "mid-name": string;
  "mid-initial": string;
  age: string;
  "pos-address": string;
  day: string;
  month: string;
  year: string;
  mayor: string;
  "ctc-no": string;
  "or-no": string;
  image?: string;
}
