import { FormSectionProps } from "../types";
import { VALIDATION_RULES } from "../constants";

export const LocationSection: React.FC<FormSectionProps> = ({
  form,
  handleTextChange,
}) => (
  <div className="border border-border rounded-lg p-4 bg-card">
    <h3 className="text-md font-medium mb-3 text-card-foreground">
      Location Information
    </h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label htmlFor="prov" className="block text-sm font-medium mb-2">
          Province *
        </label>
        <input
          id="prov"
          type="text"
          name="prov"
          placeholder="Enter province"
          value={form.prov}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
          required
        />
      </div>
      <div>
        <label htmlFor="municipal" className="block text-sm font-medium mb-2">
          Municipality *
        </label>
        <input
          id="municipal"
          type="text"
          name="municipal"
          placeholder="Enter municipality"
          value={form.municipal}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
          required
        />
      </div>
    </div>
  </div>
);

export const PersonalSection: React.FC<FormSectionProps> = ({
  form,
  handleTextChange,
}) => (
  <div className="border border-border rounded-lg p-4 bg-card">
    <h3 className="text-md font-medium mb-3 text-card-foreground">
      Personal Information
    </h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label htmlFor="first-name" className="block text-sm font-medium mb-2">
          First Name
        </label>
        <input
          id="first-name"
          type="text"
          name="first-name"
          placeholder="Enter first name"
          value={form["first-name"]}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
        />
      </div>
      <div>
        <label htmlFor="last-name" className="block text-sm font-medium mb-2">
          Last Name *
        </label>
        <input
          id="last-name"
          type="text"
          name="last-name"
          placeholder="Enter last name"
          value={form["last-name"]}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
          required
        />
      </div>
      <div>
        <label htmlFor="mid-initial" className="block text-sm font-medium mb-2">
          Middle Initial
        </label>
        <input
          id="mid-initial"
          type="text"
          name="mid-initial"
          placeholder="M.I."
          value={form["mid-initial"]}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
          maxLength={VALIDATION_RULES.MAX_INITIAL_LENGTH}
        />
      </div>
      <div>
        <label htmlFor="age" className="block text-sm font-medium mb-2">
          Age
        </label>
        <input
          id="age"
          type="number"
          name="age"
          placeholder="Enter age"
          value={form.age}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
          min={VALIDATION_RULES.MIN_AGE}
          max={VALIDATION_RULES.MAX_AGE}
        />
      </div>
    </div>
    <div className="mt-4">
      <label htmlFor="pos-address" className="block text-sm font-medium mb-2">
        Postal Address
      </label>
      <input
        id="pos-address"
        type="text"
        name="pos-address"
        placeholder="Enter postal address"
        value={form["pos-address"]}
        onChange={handleTextChange}
        className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
      />
    </div>
  </div>
);

export const DateSection: React.FC<FormSectionProps> = ({
  form,
  handleTextChange,
}) => (
  <div className="border border-border rounded-lg p-4 bg-card">
    <h3 className="text-md font-medium mb-3 text-card-foreground">
      Date Information
    </h3>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label htmlFor="day" className="block text-sm font-medium mb-2">
          Day
        </label>
        <input
          id="day"
          type="number"
          name="day"
          placeholder="DD"
          value={form.day}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
          min="1"
          max="31"
        />
      </div>
      <div>
        <label htmlFor="month" className="block text-sm font-medium mb-2">
          Month
        </label>
        <input
          id="month"
          type="text"
          name="month"
          placeholder="Enter month"
          value={form.month}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
        />
      </div>
      <div>
        <label htmlFor="year" className="block text-sm font-medium mb-2">
          Year
        </label>
        <input
          id="year"
          type="number"
          name="year"
          placeholder="YYYY"
          value={form.year}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
          min="1900"
          max="2100"
        />
      </div>
    </div>
  </div>
);

export const OfficialSection: React.FC<FormSectionProps> = ({
  form,
  handleTextChange,
}) => (
  <div className="border border-border rounded-lg p-4 bg-card">
    <h3 className="text-md font-medium mb-3 text-card-foreground">
      Official Information
    </h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label htmlFor="mayor" className="block text-sm font-medium mb-2">
          Mayor
        </label>
        <input
          id="mayor"
          type="text"
          name="mayor"
          placeholder="Enter mayor name"
          value={form.mayor}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
        />
      </div>
      <div>
        <label htmlFor="ctc-no" className="block text-sm font-medium mb-2">
          CTC No.
        </label>
        <input
          id="ctc-no"
          type="text"
          name="ctc-no"
          placeholder="Enter CTC number"
          value={form["ctc-no"]}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
        />
      </div>
      <div>
        <label htmlFor="or-no" className="block text-sm font-medium mb-2">
          O.R. No.
        </label>
        <input
          id="or-no"
          type="text"
          name="or-no"
          placeholder="Enter O.R. number"
          value={form["or-no"]}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
        />
      </div>
      <div>
        <label htmlFor="tin-no" className="block text-sm font-medium mb-2">
          TIN No. (Optional)
        </label>
        <input
          id="tin-no"
          type="text"
          name="tin-no"
          placeholder="Enter TIN number"
          value={form["tin-no"] || ""}
          onChange={handleTextChange}
          className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
        />
      </div>
    </div>
  </div>
);
