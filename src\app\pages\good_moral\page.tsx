"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useGoodMoralForm } from "./hooks/useGoodMoralForm";
import {
  ImageUpload,
  LocationSection,
  PersonalSection,
  DateSection,
  OfficialSection,
} from "./components";

/**
 * Good Moral Certificate application page
 * Provides a form interface for users to input their information
 * and generate a PDF certificate
 */
export default function GoodMoralPage() {
  const {
    form,
    isLoading,
    handleTextChange,
    handleImageUpload,
    handleGenerateDocument,
  } = useGoodMoralForm();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6 text-center">
        Good Moral Certificate
      </h1>

      <div className="max-w-2xl mx-auto">
        <div className="space-y-6">
          <h2 className="text-lg font-semibold mb-4">Fill in the details</h2>

          <ImageUpload image={form.image} onImageUpload={handleImageUpload} />

          <LocationSection form={form} handleTextChange={handleTextChange} />

          <PersonalSection form={form} handleTextChange={handleTextChange} />

          <DateSection form={form} handleTextChange={handleTextChange} />

          <OfficialSection form={form} handleTextChange={handleTextChange} />

          <Button
            onClick={handleGenerateDocument}
            disabled={isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? "Generating..." : "Generate & Download"}
          </Button>
        </div>
      </div>
    </div>
  );
}
