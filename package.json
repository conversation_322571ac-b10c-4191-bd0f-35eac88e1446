{"name": "ldis", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@deemlol/next-icons": "^0.1.9", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "docx-pdf": "^0.0.1", "docxtemplater": "^3.65.2", "docxtemplater-image-module-free": "^1.1.1", "file-saver": "^2.0.5", "html-pdf-node": "^1.0.8", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "next": "15.3.5", "pdf-lib": "^1.17.1", "pizzip": "^3.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}