/**
 * Types for PDF Viewer module
 */

export type CertificateType = 
  | "good_moral" 
  | "affidavit" 
  | "clearance" 
  | "license"
  | "business_permit"
  | "barangay_clearance";

export interface PDFViewerProps {
  pdfUrl?: string;
  pdfBlob?: Blob;
  certificateType: CertificateType;
  fileName?: string;
  onClose?: () => void;
  onDownload?: () => void;
  onPrint?: () => void;
}

export interface PDFPreviewProps {
  certificateType: CertificateType;
  formData: Record<string, any>;
  onGenerate?: (pdfBlob: Blob) => void;
  showPreview?: boolean;
}

export interface PDFControlsProps {
  onDownload?: () => void;
  onPrint?: () => void;
  onClose?: () => void;
  fileName?: string;
  isLoading?: boolean;
}

export interface PDFViewerState {
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  scale: number;
  pdfDocument: any; // PDF.js document type
}
