/**
 * PDF Generator utilities for different certificate types
 */

import { CertificateType } from "../types";

// Import certificate generators
// These would be the actual generator functions from each certificate module
type GeneratorFunction = (formData: any) => Promise<Blob>;

/**
 * Registry of PDF generators for different certificate types
 */
const PDF_GENERATORS: Record<CertificateType, GeneratorFunction> = {
  good_moral: async (formData) => {
    // Import and use the good moral generator
    const { generateDocument } = await import("../../good_moral/utils/documentGenerator");
    
    // Convert the existing generateDocument to return a Blob
    return new Promise((resolve, reject) => {
      try {
        // This is a placeholder - you'll need to modify the actual generator
        // to return a Blob instead of directly downloading
        generateDocument(formData);
        
        // For now, return a mock blob
        const mockBlob = new Blob(["PDF content"], { type: "application/pdf" });
        resolve(mockBlob);
      } catch (error) {
        reject(error);
      }
    });
  },
  
  affidavit: async (formData) => {
    // Placeholder for affidavit generator
    return new Blob(["Affidavit PDF content"], { type: "application/pdf" });
  },
  
  clearance: async (formData) => {
    // Placeholder for clearance generator
    return new Blob(["Clearance PDF content"], { type: "application/pdf" });
  },
  
  license: async (formData) => {
    // Placeholder for license generator
    return new Blob(["License PDF content"], { type: "application/pdf" });
  },
  
  business_permit: async (formData) => {
    // Placeholder for business permit generator
    return new Blob(["Business Permit PDF content"], { type: "application/pdf" });
  },
  
  barangay_clearance: async (formData) => {
    // Placeholder for barangay clearance generator
    return new Blob(["Barangay Clearance PDF content"], { type: "application/pdf" });
  },
};

/**
 * Generate PDF for a specific certificate type
 * @param certificateType - Type of certificate to generate
 * @param formData - Form data for the certificate
 * @returns Promise<Blob> - Generated PDF as blob
 */
export const generatePDF = async (
  certificateType: CertificateType,
  formData: any
): Promise<Blob> => {
  const generator = PDF_GENERATORS[certificateType];
  
  if (!generator) {
    throw new Error(`No generator found for certificate type: ${certificateType}`);
  }
  
  try {
    return await generator(formData);
  } catch (error) {
    console.error(`Error generating ${certificateType} PDF:`, error);
    throw new Error(`Failed to generate ${certificateType} certificate`);
  }
};

/**
 * Check if a certificate type has a generator
 * @param certificateType - Type of certificate to check
 * @returns boolean - Whether generator exists
 */
export const hasGenerator = (certificateType: CertificateType): boolean => {
  return certificateType in PDF_GENERATORS;
};

/**
 * Get available certificate types
 * @returns Array of available certificate types
 */
export const getAvailableCertificateTypes = (): CertificateType[] => {
  return Object.keys(PDF_GENERATORS) as CertificateType[];
};
