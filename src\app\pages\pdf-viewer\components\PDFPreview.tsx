"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, FileText } from "@deemlol/next-icons";
import { PDFPreviewProps } from "../types";
import { PDFViewer } from "./PDFViewer";
import { CERTIFICATE_CONFIGS } from "../constants";

/**
 * PDF Preview component for showing generated certificates
 */
export const PDFPreview: React.FC<PDFPreviewProps> = ({
  certificateType,
  formData,
  onGenerate,
  showPreview = false,
}) => {
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showViewer, setShowViewer] = useState(showPreview);

  const certificateConfig = CERTIFICATE_CONFIGS[certificateType];

  const handleGeneratePreview = async () => {
    setIsGenerating(true);
    
    try {
      // This would be replaced with actual certificate generation logic
      // For now, we'll simulate PDF generation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real implementation, you would call the appropriate generator
      // based on certificateType and pass the formData
      const mockPdfBlob = new Blob(["Mock PDF content"], { type: "application/pdf" });
      
      setPdfBlob(mockPdfBlob);
      setShowViewer(true);
      onGenerate?.(mockPdfBlob);
    } catch (error) {
      console.error("Error generating preview:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCloseViewer = () => {
    setShowViewer(false);
  };

  if (showViewer && pdfBlob) {
    return (
      <div className="fixed inset-0 z-50 bg-white">
        <PDFViewer
          pdfBlob={pdfBlob}
          certificateType={certificateType}
          fileName={`${certificateConfig.defaultFileName}_Preview`}
          onClose={handleCloseViewer}
        />
      </div>
    );
  }

  return (
    <div className="border border-gray-200 rounded-lg p-6 bg-white">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <FileText className="w-6 h-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-800">
              {certificateConfig.name}
            </h3>
            <p className="text-sm text-gray-600">
              {certificateConfig.description}
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {/* Form Data Summary */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            Document Information
          </h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {Object.entries(formData).slice(0, 6).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-gray-600 capitalize">
                  {key.replace(/[-_]/g, " ")}:
                </span>
                <span className="text-gray-800 font-medium">
                  {String(value).slice(0, 20)}
                  {String(value).length > 20 ? "..." : ""}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Preview Actions */}
        <div className="flex space-x-3">
          <Button
            onClick={handleGeneratePreview}
            disabled={isGenerating || !certificateConfig.previewEnabled}
            className="flex items-center space-x-2"
          >
            <Eye className="w-4 h-4" />
            <span>
              {isGenerating ? "Generating..." : "Preview Document"}
            </span>
          </Button>

          {pdfBlob && (
            <Button
              variant="outline"
              onClick={() => setShowViewer(true)}
              className="flex items-center space-x-2"
            >
              <FileText className="w-4 h-4" />
              <span>View Generated</span>
            </Button>
          )}
        </div>

        {!certificateConfig.previewEnabled && (
          <p className="text-sm text-amber-600">
            Preview is not available for this certificate type.
          </p>
        )}
      </div>
    </div>
  );
};
