/**
 * Constants for Good Moral Certificate application
 */

// Document dimensions (A4 at 96 DPI)
export const DOCUMENT_DIMENSIONS = {
  WIDTH: 794,
  HEIGHT: 1123,
  PADDING: 60,
} as const;

// PDF export settings
export const PDF_SETTINGS = {
  FORMAT: "a4" as const,
  ORIENTATION: "portrait" as const,
  SCALE: 2,
  IMAGE_WIDTH: 595,
  IMAGE_HEIGHT: 842,
} as const;

// Image processing settings
export const IMAGE_SETTINGS = {
  SIZE: 192,
  QUALITY: 0.8,
  FORMAT: "image/jpeg" as const,
} as const;

// Form validation rules
export const VALIDATION_RULES = {
  REQUIRED_FIELDS: ["last-name", "prov", "municipal"] as const,
  MAX_AGE: 120,
  MIN_AGE: 1,
  MAX_INITIAL_LENGTH: 2,
} as const;

// Default values
export const DEFAULT_VALUES = {
  LOCATION: "Tanauan, Leyte, Philippines",
  NATIONALITY: "Filipino",
} as const;
