"use client";

import { useState } from "react";
import { PDFViewer, PDFPreview } from "./components";
import { CertificateType } from "./types";
import { CERTIFICATE_CONFIGS } from "./constants";

/**
 * PDF Viewer demo page
 */
export default function PDFViewerPage() {
  const [selectedType, setSelectedType] = useState<CertificateType>("good_moral");
  const [showViewer, setShowViewer] = useState(false);
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);

  const mockFormData = {
    "first-name": "<PERSON>",
    "last-name": "<PERSON><PERSON>",
    "mid-initial": "M",
    age: "25",
    municipal: "Sample City",
    prov: "Sample Province",
    "pos-address": "123 Sample Street",
    day: "15",
    month: "January",
    year: "2024",
    mayor: "<PERSON> Smith",
    "ctc-no": "123456789",
    "or-no": "987654321",
  };

  const handleGeneratePDF = (blob: Blob) => {
    setPdfBlob(blob);
    setShowViewer(true);
  };

  const handleCloseViewer = () => {
    setShowViewer(false);
  };

  if (showViewer && pdfBlob) {
    return (
      <PDFViewer
        pdfBlob={pdfBlob}
        certificateType={selectedType}
        onClose={handleCloseViewer}
      />
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          PDF Viewer Demo
        </h1>
        <p className="text-gray-600">
          Test the PDF viewer with different certificate types
        </p>
      </div>

      {/* Certificate Type Selector */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Certificate Type
        </label>
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value as CertificateType)}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {Object.entries(CERTIFICATE_CONFIGS).map(([key, config]) => (
            <option key={key} value={key}>
              {config.name} - {config.description}
            </option>
          ))}
        </select>
      </div>

      {/* PDF Preview Component */}
      <PDFPreview
        certificateType={selectedType}
        formData={mockFormData}
        onGenerate={handleGeneratePDF}
      />

      {/* Instructions */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">
          How to Use
        </h3>
        <ul className="text-blue-700 space-y-1">
          <li>• Select a certificate type from the dropdown</li>
          <li>• Click "Preview Document" to generate a preview</li>
          <li>• Use the viewer controls to navigate, zoom, and download</li>
          <li>• The viewer supports all future certificate types</li>
        </ul>
      </div>
    </div>
  );
}
