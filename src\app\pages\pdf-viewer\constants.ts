/**
 * Constants for PDF Viewer module
 */

import { CertificateType } from "./types";

// PDF viewer settings
export const PDF_VIEWER_SETTINGS = {
  DEFAULT_SCALE: 1.0,
  MIN_SCALE: 0.5,
  MAX_SCALE: 3.0,
  SCALE_STEP: 0.25,
  CANVAS_MAX_WIDTH: 1200,
  CANVAS_MAX_HEIGHT: 1600,
} as const;

// Certificate type configurations
export const CERTIFICATE_CONFIGS: Record<CertificateType, {
  name: string;
  description: string;
  defaultFileName: string;
  previewEnabled: boolean;
}> = {
  good_moral: {
    name: "Good Moral Certificate",
    description: "Certificate of Good Moral Character/Clearance",
    defaultFileName: "Good_Moral_Certificate",
    previewEnabled: true,
  },
  affidavit: {
    name: "Affidavit",
    description: "Legal Affidavit Document",
    defaultFileName: "Affidavit",
    previewEnabled: true,
  },
  clearance: {
    name: "Clearance",
    description: "Official Clearance Document",
    defaultFileName: "Clearance",
    previewEnabled: true,
  },
  license: {
    name: "License",
    description: "Official License Document",
    defaultFileName: "License",
    previewEnabled: true,
  },
  business_permit: {
    name: "Business Permit",
    description: "Business Permit Certificate",
    defaultFileName: "Business_Permit",
    previewEnabled: true,
  },
  barangay_clearance: {
    name: "Barangay Clearance",
    description: "Barangay Clearance Certificate",
    defaultFileName: "Barangay_Clearance",
    previewEnabled: true,
  },
} as const;

// File type configurations
export const FILE_CONFIGS = {
  PDF: {
    mimeType: "application/pdf",
    extension: ".pdf",
    maxSize: 10 * 1024 * 1024, // 10MB
  },
} as const;
