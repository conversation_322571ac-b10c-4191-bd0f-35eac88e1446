"use client";

import { PDFViewerProps } from "../types";
import { usePDFViewer } from "../hooks/usePDFViewer";
import { PDFControls } from "./PDFControls";
import { PDFCanvas } from "./PDFCanvas";
import { CERTIFICATE_CONFIGS } from "../constants";

/**
 * Main PDF Viewer component
 */
export const PDFViewer: React.FC<PDFViewerProps> = ({
  pdfUrl,
  pdfBlob,
  certificateType,
  fileName,
  onClose,
  onDownload,
  onPrint,
}) => {
  const {
    isLoading,
    error,
    currentPage,
    totalPages,
    scale,
    pdfDocument,
    goToPage,
    nextPage,
    prevPage,
    zoomIn,
    zoomOut,
    resetZoom,
  } = usePDFViewer(pdfUrl, pdfBlob);

  const certificateConfig = CERTIFICATE_CONFIGS[certificateType];
  const displayFileName = fileName || certificateConfig.defaultFileName;

  const handleDownload = () => {
    if (pdfBlob) {
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${displayFileName}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } else if (pdfUrl) {
      const link = document.createElement("a");
      link.href = pdfUrl;
      link.download = `${displayFileName}.pdf`;
      link.click();
    }
    onDownload?.();
  };

  const handlePrint = () => {
    if (pdfBlob) {
      const url = URL.createObjectURL(pdfBlob);
      const printWindow = window.open(url);
      printWindow?.addEventListener("load", () => {
        printWindow.print();
      });
    } else if (pdfUrl) {
      const printWindow = window.open(pdfUrl);
      printWindow?.addEventListener("load", () => {
        printWindow.print();
      });
    }
    onPrint?.();
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <div className="text-red-600 text-lg font-semibold mb-2">Error Loading PDF</div>
        <div className="text-gray-600 text-center mb-4">{error}</div>
        {onClose && (
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Close
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h2 className="text-lg font-semibold text-gray-800">
          {certificateConfig.name}
        </h2>
        <p className="text-sm text-gray-600">{certificateConfig.description}</p>
      </div>

      {/* Controls */}
      {!isLoading && pdfDocument && (
        <PDFControls
          onDownload={handleDownload}
          onPrint={handlePrint}
          onClose={onClose}
          fileName={displayFileName}
          isLoading={isLoading}
          currentPage={currentPage}
          totalPages={totalPages}
          scale={scale}
          onPageChange={goToPage}
          onZoomIn={zoomIn}
          onZoomOut={zoomOut}
          onResetZoom={resetZoom}
          onNextPage={nextPage}
          onPrevPage={prevPage}
        />
      )}

      {/* PDF Content */}
      <div className="flex-1 overflow-auto p-4">
        {isLoading ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <div className="text-gray-600">Loading PDF...</div>
            </div>
          </div>
        ) : pdfDocument ? (
          <PDFCanvas
            pdfDocument={pdfDocument}
            pageNumber={currentPage}
            scale={scale}
            onLoadError={(error) => console.error("Canvas error:", error)}
          />
        ) : null}
      </div>
    </div>
  );
};
