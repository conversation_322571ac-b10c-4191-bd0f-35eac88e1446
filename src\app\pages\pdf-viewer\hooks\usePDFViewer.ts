import { useState, useEffect, useCallback } from "react";
import { PDFViewerState } from "../types";

/**
 * Custom hook for managing PDF viewer state and actions
 */
export const usePDFViewer = (pdfUrl?: string, pdfBlob?: Blob) => {
  const [state, setState] = useState<PDFViewerState>({
    isLoading: true,
    error: null,
    currentPage: 1,
    totalPages: 0,
    scale: 1.0,
    pdfDocument: null,
  });

  /**
   * Load PDF document from URL or Blob
   */
  const loadPDF = useCallback(async () => {
    if (!pdfUrl && !pdfBlob) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: "No PDF source provided",
      }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Dynamic import of PDF.js to avoid SSR issues
      const pdfjsLib = await import("pdfjs-dist");
      
      // Set worker source
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

      let pdfData: string | ArrayBuffer;
      
      if (pdfBlob) {
        pdfData = await pdfBlob.arrayBuffer();
      } else if (pdfUrl) {
        const response = await fetch(pdfUrl);
        pdfData = await response.arrayBuffer();
      } else {
        throw new Error("No valid PDF source");
      }

      const pdf = await pdfjsLib.getDocument({ data: pdfData }).promise;

      setState(prev => ({
        ...prev,
        isLoading: false,
        pdfDocument: pdf,
        totalPages: pdf.numPages,
        currentPage: 1,
      }));
    } catch (error) {
      console.error("Error loading PDF:", error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to load PDF",
      }));
    }
  }, [pdfUrl, pdfBlob]);

  /**
   * Navigate to specific page
   */
  const goToPage = useCallback((pageNumber: number) => {
    setState(prev => ({
      ...prev,
      currentPage: Math.max(1, Math.min(pageNumber, prev.totalPages)),
    }));
  }, []);

  /**
   * Navigate to next page
   */
  const nextPage = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentPage: Math.min(prev.currentPage + 1, prev.totalPages),
    }));
  }, []);

  /**
   * Navigate to previous page
   */
  const prevPage = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentPage: Math.max(prev.currentPage - 1, 1),
    }));
  }, []);

  /**
   * Zoom in
   */
  const zoomIn = useCallback(() => {
    setState(prev => ({
      ...prev,
      scale: Math.min(prev.scale + 0.25, 3.0),
    }));
  }, []);

  /**
   * Zoom out
   */
  const zoomOut = useCallback(() => {
    setState(prev => ({
      ...prev,
      scale: Math.max(prev.scale - 0.25, 0.5),
    }));
  }, []);

  /**
   * Reset zoom to default
   */
  const resetZoom = useCallback(() => {
    setState(prev => ({ ...prev, scale: 1.0 }));
  }, []);

  // Load PDF when URL or Blob changes
  useEffect(() => {
    loadPDF();
  }, [loadPDF]);

  return {
    ...state,
    goToPage,
    nextPage,
    prevPage,
    zoomIn,
    zoomOut,
    resetZoom,
    reload: loadPDF,
  };
};
