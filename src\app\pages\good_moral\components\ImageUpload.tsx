import NextImage from "next/image";

interface ImageUploadProps {
  image: string;
  onImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({ image, onImageUpload }) => {
  return (
    <div className="flex justify-center mb-6">
      <div>
        <input
          id="image"
          type="file"
          accept="image/*"
          onChange={onImageUpload}
          className="hidden"
        />
        <div
          onClick={() => document.getElementById("image")?.click()}
          className="w-32 h-32 border-2 border-dashed border-border rounded-lg flex items-center justify-center cursor-pointer hover:border-primary transition-colors bg-card"
        >
          {image ? (
            <NextImage
              src={`data:image/jpeg;base64,${image}`}
              alt="Preview"
              width={128}
              height={128}
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <div className="text-center text-muted-foreground">
              <svg
                className="w-8 h-8 mx-auto mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <p className="text-sm">Click to upload photo</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
